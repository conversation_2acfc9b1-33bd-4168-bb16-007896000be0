local EventSystem = {}

function EventSystem:new()
    local self = {
        handlers = {},
        middleware = {}
    }
    setmetatable(self, {__index = EventSystem})
    return self
end

function EventSystem:on(event, handler)
    if not self.handlers[event] then
        self.handlers[event] = {}
    end
    table.insert(self.handlers[event], handler)
end

function EventSystem:emit(event, data)
    local handlers = self.handlers[event] or {}
    for _, handler in ipairs(handlers) do
        coroutine.wrap(handler)(data)
    end
end

return EventSystem